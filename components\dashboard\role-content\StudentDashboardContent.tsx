"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { DocumentWorkflowStatus } from "@/components/documents/DocumentWorkflowStatus";
import { Button } from "@/components/ui/button";
import { FileText, Calendar, Clock, ChevronRight, BarChart3 } from "lucide-react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { Project, Document, User, DocumentStatus, ProjectMilestone } from "@/lib/types";
import { StudentAnalyticsView } from "@/components/analytics/StudentAnalyticsView";

interface StudentDashboardContentProps {
  projects: Project[];
  documents: Document[];
  supervisors: User[];
  isLoading: boolean;
}

// Helper function to safely format dates
const safeFormatDate = (dateValue: string | Date | undefined, formatString = "PP") => {
  if (!dateValue) return "Never";
  try {
    return format(new Date(dateValue), formatString);
  } catch (error) {
    console.warn("Invalid date format:", dateValue);
    return "Invalid date";
  }
};

// Helper function to find the next deadline (project or milestone)
const getNextDeadline = (projects: Project[]) => {
  const now = new Date();
  const allDeadlines: Array<{
    date: Date;
    title: string;
    type: 'project' | 'milestone';
    projectTitle?: string;
    isOverdue: boolean;
    daysOverdue?: number;
  }> = [];

  projects.forEach((project) => {
    // Add project deadline
    if (project.deadline) {
      const deadline = new Date(project.deadline);
      if (project.status !== 'completed') {
        const isOverdue = deadline < now;
        const daysOverdue = isOverdue ? Math.floor((now.getTime() - deadline.getTime()) / (1000 * 60 * 60 * 24)) : 0;

        allDeadlines.push({
          date: deadline,
          title: project.title,
          type: 'project',
          isOverdue,
          daysOverdue
        });
      }
    }

    // Add milestone deadlines
    if (project.milestones && project.milestones.length > 0) {
      project.milestones.forEach((milestone: ProjectMilestone) => {
        if (!milestone.completed) {
          const milestoneDate = new Date(milestone.dueDate);
          const isOverdue = milestoneDate < now;
          const daysOverdue = isOverdue ? Math.floor((now.getTime() - milestoneDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;

          allDeadlines.push({
            date: milestoneDate,
            title: milestone.title,
            type: 'milestone',
            projectTitle: project.title,
            isOverdue,
            daysOverdue
          });
        }
      });
    }
  });

  // Sort by priority: overdue items first (by how overdue), then upcoming by date
  allDeadlines.sort((a, b) => {
    if (a.isOverdue && !b.isOverdue) return -1;
    if (!a.isOverdue && b.isOverdue) return 1;
    if (a.isOverdue && b.isOverdue) return (b.daysOverdue || 0) - (a.daysOverdue || 0);
    return a.date.getTime() - b.date.getTime();
  });

  return allDeadlines[0] || null;
};

// Document card component
const DocumentCard = ({ doc, onClick }: { doc: Document; onClick: () => void }) => (
  <div
    className="flex items-center gap-4 p-3 rounded-lg border bg-card hover:bg-accent/5 transition-colors cursor-pointer"
    onClick={onClick}
  >
    <FileText className="h-10 w-10 text-primary/70" />
    <div className="flex-1 min-w-0">
      <p className="font-medium truncate">{doc.title}</p>
      <p className="text-sm text-muted-foreground">
        Last updated: {safeFormatDate(doc.lastModified)}
      </p>
    </div>
    <div className="flex flex-col items-end gap-1">
      <DocumentWorkflowStatus
        currentStatus={doc.status as DocumentStatus}
        compact={true}
      />
    </div>
  </div>
);

export function StudentDashboardContent({
  projects,
  documents,
  supervisors,
  isLoading
}: StudentDashboardContentProps) {
  const router = useRouter();

  // Get the next deadline (project or milestone)
  const nextDeadline = getNextDeadline(projects);

  return (
    <Tabs defaultValue="overview" className="space-y-6">
      <TabsList>
        <TabsTrigger value="overview" className="flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          Overview
        </TabsTrigger>
        <TabsTrigger value="analytics" className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4" />
          My Analytics
        </TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-6">
        {/* Student-specific stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Projects</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{projects.length}</div>
            <p className="text-xs text-muted-foreground">
              {projects.filter(p => p.status === "active").length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Documents</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{documents.length}</div>
            <p className="text-xs text-muted-foreground">
              {documents.filter(d => d.status === "approved").length} approved
            </p>
          </CardContent>
        </Card>

        <Card className={nextDeadline?.isOverdue ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20' : ''}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className={`text-sm font-medium ${nextDeadline?.isOverdue ? 'text-red-700 dark:text-red-300' : ''}`}>
              {nextDeadline?.isOverdue ? 'Overdue Deadline!' : 'Next Deadline'}
            </CardTitle>
            <div className="flex items-center gap-1">
              {nextDeadline?.isOverdue && <span className="text-red-500 text-sm">⚠️</span>}
              <Clock className={`h-4 w-4 ${nextDeadline?.isOverdue ? 'text-red-500' : 'text-muted-foreground'}`} />
            </div>
          </CardHeader>
          <CardContent>
            {nextDeadline ? (
              <>
                <div className={`text-2xl font-bold ${nextDeadline.isOverdue ? 'text-red-600 dark:text-red-400' : ''}`}>
                  {nextDeadline.isOverdue ?
                    `${nextDeadline.daysOverdue}d overdue` :
                    safeFormatDate(nextDeadline.date, "MMM d")
                  }
                </div>
                <p className={`text-xs ${nextDeadline.isOverdue ? 'text-red-600 dark:text-red-400' : 'text-muted-foreground'}`}>
                  {nextDeadline.title}
                  {nextDeadline.type === 'milestone' && nextDeadline.projectTitle && (
                    <span className="block">Project: {nextDeadline.projectTitle}</span>
                  )}
                </p>
                <div className="flex items-center justify-between mt-1">
                  <p className={`text-xs capitalize ${nextDeadline.isOverdue ? 'text-red-600 dark:text-red-400' : 'text-muted-foreground'}`}>
                    {nextDeadline.type}
                  </p>
                  {nextDeadline.isOverdue && (
                    <p className="text-xs text-red-600 dark:text-red-400">
                      Due: {safeFormatDate(nextDeadline.date, "MMM d")}
                    </p>
                  )}
                </div>
              </>
            ) : (
              <>
                <div className="text-2xl font-bold">-</div>
                <p className="text-xs text-muted-foreground">No upcoming deadlines</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Documents */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Documents</CardTitle>
          <CardDescription>Your latest submissions and drafts</CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[200px]">
            <div className="space-y-4">
              {documents.length > 0 ? (
                documents.slice(0, 5).map((doc) => (
                  <DocumentCard
                    key={doc.id}
                    doc={doc}
                    onClick={() => router.push(`/documents/${doc.id}`)}
                  />
                ))
              ) : (
                <p className="text-center text-muted-foreground py-4">No documents found</p>
              )}
            </div>
          </ScrollArea>
          {documents.length > 0 && (
            <Button
              variant="outline"
              className="w-full mt-4"
              onClick={() => router.push('/documents')}
            >
              View All Documents
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </CardContent>
      </Card>
      </TabsContent>

      <TabsContent value="analytics" className="space-y-6">
        <StudentAnalyticsView timeRange="month" />
      </TabsContent>
    </Tabs>
  );
}
