"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { FileText, Users, Clock, Calendar, CheckCircle2, AlertCircle, BarChart3 } from "lucide-react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { Project, Document, User } from "@/lib/types";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SupervisorAnalyticsView } from "@/components/analytics/SupervisorAnalyticsView";

interface SupervisorDashboardContentProps {
  projects: Project[];
  documents: Document[];
  students: User[];
  isLoading: boolean;
}

// Helper function to safely format dates
const safeFormatDate = (dateValue: string | Date | undefined, formatString = "PP") => {
  if (!dateValue) return "Never";
  try {
    return format(new Date(dateValue), formatString);
  } catch (error) {
    console.warn("Invalid date format:", dateValue);
    return "Invalid date";
  }
};

// Stats card component
const StatsCard = ({ 
  title, 
  value, 
  icon, 
  description 
}: { 
  title: string; 
  value: number | string; 
  icon: React.ReactNode; 
  description: string;
}) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      <p className="text-xs text-muted-foreground">{description}</p>
    </CardContent>
  </Card>
);

// Document review card component
const DocumentReviewCard = ({ 
  doc, 
  student, 
  onClick 
}: { 
  doc: Document; 
  student?: User; 
  onClick: () => void;
}) => (
  <div
    className="flex items-center gap-4 p-3 rounded-lg border bg-card hover:bg-accent/5 transition-colors cursor-pointer"
    onClick={onClick}
  >
    <div className="flex-shrink-0">
      <Avatar>
        <AvatarImage src={student?.profileImage} alt={student?.name} />
        <AvatarFallback>{student?.name?.substring(0, 2).toUpperCase() || "ST"}</AvatarFallback>
      </Avatar>
    </div>
    <div className="flex-1 min-w-0">
      <p className="font-medium truncate">{doc.title}</p>
      <p className="text-sm text-muted-foreground">
        {student?.name || "Unknown Student"} • {safeFormatDate(doc.lastModified)}
      </p>
    </div>
    <div className="flex flex-col items-end gap-1">
      <Badge
        variant={doc.status === "under_review" ? "secondary" : "outline"}
        className="capitalize whitespace-nowrap"
      >
        {doc.status.replace("_", " ")}
      </Badge>
    </div>
  </div>
);

export function SupervisorDashboardContent({ 
  projects, 
  documents, 
  students,
  isLoading 
}: SupervisorDashboardContentProps) {
  const router = useRouter();
  
  // Calculate metrics
  const metrics = {
    activeProjects: projects.filter(p => p.status === "active").length,
    totalProjects: projects.length,
    pendingReviews: documents.filter(d => d.status === "under_review").length,
    totalDocuments: documents.length
  };
  
  // Get documents that need review
  const reviewQueue = documents
    .filter(d => d.status === "under_review")
    .sort((a, b) => new Date(a.lastModified).getTime() - new Date(b.lastModified).getTime());
  
  return (
    <Tabs defaultValue="overview" className="space-y-6">
      <TabsList>
        <TabsTrigger value="overview" className="flex items-center gap-2">
          <Users className="h-4 w-4" />
          Overview
        </TabsTrigger>
        <TabsTrigger value="analytics" className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4" />
          Supervision Analytics
        </TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-6">
        {/* Supervisor-specific stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Review Queue"
          value={metrics.pendingReviews}
          icon={<FileText className="h-4 w-4 text-muted-foreground" />}
          description={`${metrics.totalDocuments} total documents`}
        />
        <StatsCard
          title="Active Projects"
          value={metrics.activeProjects}
          icon={<Calendar className="h-4 w-4 text-muted-foreground" />}
          description={`${metrics.totalProjects} total projects`}
        />
        <StatsCard
          title="Supervised Students"
          value={students.length}
          icon={<Users className="h-4 w-4 text-muted-foreground" />}
          description={`Across ${projects.length} projects`}
        />
        <StatsCard
          title="Review Time"
          value="2.5 days"
          icon={<Clock className="h-4 w-4 text-muted-foreground" />}
          description="Average response time"
        />
      </div>

      {/* Review Queue */}
      <Card>
        <CardHeader>
          <CardTitle>Review Queue</CardTitle>
          <CardDescription>Documents awaiting your review</CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[300px]">
            <div className="space-y-4">
              {reviewQueue.length > 0 ? (
                reviewQueue.map((doc) => {
                  const student = students.find(s => s.id === doc.studentId);
                  return (
                    <DocumentReviewCard
                      key={doc.id}
                      doc={doc}
                      student={student}
                      onClick={() => router.push(`/documents/${doc.id}`)}
                    />
                  );
                })
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <CheckCircle2 className="h-12 w-12 text-primary/50 mb-2" />
                  <p className="font-medium">All caught up!</p>
                  <p className="text-sm text-muted-foreground">
                    No documents waiting for review
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>
          {reviewQueue.length > 0 && (
            <Button 
              variant="outline" 
              className="w-full mt-4"
              onClick={() => router.push('/documents?filter=under_review')}
            >
              View All Pending Reviews
            </Button>
          )}
        </CardContent>
      </Card>
      </TabsContent>

      <TabsContent value="analytics" className="space-y-6">
        <SupervisorAnalyticsView timeRange="quarter" />
      </TabsContent>
    </Tabs>
  );
}
