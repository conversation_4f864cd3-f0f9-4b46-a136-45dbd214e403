"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Users,
  FileText,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  TrendingUp,
  Bar<PERSON>hart3,
} from "lucide-react";
import { Project, User, UserRole } from "@/lib/types";
import { useRouter } from "next/navigation";
import { useAnalytics } from "@/hooks/useAnalytics";
import { PendingSupervisorProjects } from "@/components/projects/PendingSupervisorProjects";
import { RealTimeAnalytics } from "@/components/analytics/RealTimeAnalytics";
import { ManagerAnalyticsView } from "@/components/analytics/ManagerAnalyticsView";

interface ManagerDashboardContentProps {
  users: User[];
  projects: Project[];
  isLoading: boolean;
}

export function ManagerDashboardContent({
  users,
  projects,
  isLoading,
}: ManagerDashboardContentProps) {
  const router = useRouter();

  // Get real analytics data for manager insights
  const {
    analytics,
    isLoading: isLoadingAnalytics,
    error: analyticsError
  } = useAnalytics({
    timeRange: 'quarter',
    enableRealTime: true,
    includeExtendedAnalytics: true,
    filters: { userRole: 'all' } // Managers see all data
  });

  // Calculate metrics
  const metrics = {
    totalUsers: users.length,
    totalProjects: projects.length,
    activeProjects: projects.filter((p) => p.status === "active").length,
    completedProjects: projects.filter((p) => p.status === "completed").length,
    pendingSupervisorProjects: projects.filter((p) => p.status === "pending_supervisor").length,
  };

  // Calculate real trends from analytics data
  const trends = analytics ? {
    users: analytics.userEngagement.engagementRate - 85, // Compare to baseline
    projects: analytics.projectTrends.length > 1 ?
      ((analytics.projectTrends[analytics.projectTrends.length - 1].newProjects -
        analytics.projectTrends[analytics.projectTrends.length - 2].newProjects) /
        analytics.projectTrends[analytics.projectTrends.length - 2].newProjects) * 100 : 0,
    active: analytics.projectsByStatus.active > 0 ?
      ((analytics.projectsByStatus.active / analytics.totalProjects) * 100) - 60 : 0, // Compare to 60% baseline
    completion: analytics.averageCompletionTime < 90 ? 5.0 : -2.0, // Good if under 90 days
  } : {
    users: 0,
    projects: 0,
    active: 0,
    completion: 0,
  };

  // Count users by role
  const usersByRole = users.reduce((acc, user) => {
    const role = user.role;
    acc[role] = (acc[role] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);



  return (
    <div className="space-y-6">
      {/* Manager-specific stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="overflow-hidden border-l-4 border-l-blue-500 transition-all hover:shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <div className="rounded-full bg-blue-100 p-2">
              <Users className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalUsers}</div>
            <div className="mt-1 flex items-center text-xs">
              <span
                className={`flex items-center ${
                  trends.users >= 0 ? "text-green-500" : "text-red-500"
                }`}
              >
                {trends.users >= 0 ? (
                  <ArrowUpRight className="mr-1 h-3 w-3" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3" />
                )}
                {Math.abs(trends.users)}%
              </span>
              <span className="ml-1 text-muted-foreground">
                from last month
              </span>
            </div>
            <p className="mt-2 text-xs text-muted-foreground">
              {usersByRole.student || 0} students, {usersByRole.supervisor || 0}{" "}
              supervisors
            </p>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-l-4 border-l-indigo-500 transition-all hover:shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Projects
            </CardTitle>
            <div className="rounded-full bg-indigo-100 p-2">
              <FileText className="h-4 w-4 text-indigo-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalProjects}</div>
            <div className="mt-1 flex items-center text-xs">
              <span
                className={`flex items-center ${
                  trends.projects >= 0 ? "text-green-500" : "text-red-500"
                }`}
              >
                {trends.projects >= 0 ? (
                  <ArrowUpRight className="mr-1 h-3 w-3" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3" />
                )}
                {Math.abs(trends.projects)}%
              </span>
              <span className="ml-1 text-muted-foreground">
                from last month
              </span>
            </div>
            <p className="mt-2 text-xs text-muted-foreground">
              {metrics.activeProjects} active, {metrics.completedProjects}{" "}
              completed, {metrics.pendingSupervisorProjects} pending
            </p>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-l-4 border-l-green-500 transition-all hover:shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Projects
            </CardTitle>
            <div className="rounded-full bg-green-100 p-2">
              <Activity className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeProjects}</div>
            <div className="mt-1 flex items-center text-xs">
              <span
                className={`flex items-center ${
                  trends.active >= 0 ? "text-green-500" : "text-red-500"
                }`}
              >
                {trends.active >= 0 ? (
                  <ArrowUpRight className="mr-1 h-3 w-3" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3" />
                )}
                {Math.abs(trends.active)}%
              </span>
              <span className="ml-1 text-muted-foreground">
                from last month
              </span>
            </div>
            <p className="mt-2 text-xs text-muted-foreground">
              {metrics.totalProjects > 0
                ? `${((metrics.activeProjects / metrics.totalProjects) * 100).toFixed(0)}% of total`
                : 'No projects yet'
              }
            </p>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-l-4 border-l-amber-500 transition-all hover:shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Completion Rate
            </CardTitle>
            <div className="rounded-full bg-amber-100 p-2">
              <Activity className="h-4 w-4 text-amber-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.totalProjects > 0
                ? `${((metrics.completedProjects / metrics.totalProjects) * 100).toFixed(0)}%`
                : '0%'
              }
            </div>
            <div className="mt-1 flex items-center text-xs">
              <span
                className={`flex items-center ${
                  trends.completion >= 0 ? "text-green-500" : "text-red-500"
                }`}
              >
                {trends.completion >= 0 ? (
                  <ArrowUpRight className="mr-1 h-3 w-3" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3" />
                )}
                {Math.abs(trends.completion)}%
              </span>
              <span className="ml-1 text-muted-foreground">
                from last month
              </span>
            </div>
            <p className="mt-2 text-xs text-muted-foreground">
              {metrics.completedProjects} completed projects
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Pending Supervisor Projects */}
      <PendingSupervisorProjects
        projects={projects}
        supervisors={users.filter((user) => user.role === "supervisor")}
        isLoading={isLoading}
      />

      {/* Manager Analytics Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Manager Analytics
              </CardTitle>
              <CardDescription>
                Comprehensive insights and real-time analytics for management oversight
              </CardDescription>
            </div>
            <Button
              variant="outline"
              onClick={() => router.push('/reports')}
              className="flex items-center gap-2"
            >
              <TrendingUp className="h-4 w-4" />
              View Full Reports
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {analyticsError ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>Unable to load analytics data</p>
              <p className="text-sm mt-1">{analyticsError}</p>
            </div>
          ) : (
            <ManagerAnalyticsView timeRange="year" />
          )}
        </CardContent>
      </Card>

    </div>
  );
}
